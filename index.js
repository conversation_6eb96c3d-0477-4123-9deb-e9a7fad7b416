// 模型配置数组
export const modelList = [
	// 老年男性模型
	{
		type: 'elder<PERSON>ale',
		label: '老年男',
		name: 'elder<PERSON>ale01',
		path: 'glb/老年男01.glb',
		texture: [
			{
				name: 'elder<PERSON>ale01',
				path: '贴图/老年男01_贴图.png',
			},
		],
	},
	{
		type: 'elder<PERSON>ale',
		label: '老年男',
		name: 'elderMale02',
		path: 'glb/老年男02.glb',
		texture: [
			{
				name: 'elderMale02',
				path: '贴图/老年男02_贴图.png',
			},
		],
	},
	{
		type: 'elderMale',
		label: '老年男',
		name: 'elderMale03',
		path: 'glb/唐装老年男01.glb',
		texture: [
			{
				name: 'elderMale03',
				path: '贴图/唐装老年男01_贴图.png',
			},
			{
				name: 'elderMale0302',
				path: '贴图/唐装老年男01_头发_贴图.png',
			},
		],
	},
	// 老年女性模型
	{
		type: 'elder<PERSON><PERSON><PERSON>',
		label: '老年女',
		name: 'elder<PERSON><PERSON>le01',
		path: 'glb/老年女01.glb',
		texture: [
			{
				name: 'elder<PERSON><PERSON><PERSON>01',
				path: '贴图/老年女01_贴图.png',
			},
		],
	},
	{
		type: 'elderFemale',
		label: '老年女',
		name: 'elderFemale02',
		path: 'glb/老年女02.glb',
		texture: [
			{
				name: 'elderFemale02',
				path: '贴图/老年女02_贴图.png',
			},
		],
	},
	{
		type: 'elderFemale',
		label: '老年女',
		name: 'elderFemale03',
		path: 'glb/亚洲老年女01.glb',
		texture: [
			{
				name: 'elderFemale03',
				path: '贴图/亚洲老年女01_贴图.png',
			},
		],
	},
	// 成年男性模型
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale01',
		path: 'glb/成年男02.glb',
		texture: [
			{
				name: 'adultMale01',
				path: '贴图/成年男02_贴图.png',
			},
		],
	},
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale02',
		path: 'glb/唐装成年男01.glb',
		texture: [
			{
				name: 'adultMale02',
				path: '贴图/唐装成年男01_贴图.png',
			},
			{
				name: 'adultMale0202',
				path: '贴图/唐装成年男01头发_贴图.png',
			},
		],
	},
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale03',
		path: 'glb/唐装成年男02.glb',
		texture: [
			{
				name: 'adultMale03',
				path: '贴图/唐装成年男02_贴图.png',
			},
		],
	},
	// 成年女性模型
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale01',
		path: 'glb/成年女01.glb',
		texture: [
			{
				name: 'adultFemale01',
				path: '贴图/成年女01_贴图.png',
			},
		],
	},
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale02',
		path: 'glb/旗袍女01.glb',
		texture: [
			{
				name: 'adultFemale02',
				path: '贴图/旗袍女01_贴图.png',
			},
			{
				name: 'adultFemale0202',
				path: '贴图/旗袍女01头发_贴图.png',
			},
		],
	},
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale03',
		path: 'glb/旗袍女02.glb',
		texture: [
			{
				name: 'adultFemale03',
				path: '贴图/旗袍女02_贴图.png',
			},
		],
	},

	// 儿童男性模型
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale01',
		path: 'childMale01.glb',
		texture: [
			{
				name: 'childMale01',
				path: 'childMale01.png',
			},
		],
	},
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale02',
		path: 'childMale02.glb',
		texture: [
			{
				name: 'childMale02',
				path: 'childMale02.png',
			},
		],
	},
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale03',
		path: 'childMale03.glb',
		texture: [
			{
				name: 'childMale03',
				path: 'childMale03.png',
			},
		],
	},
	// 儿童女性模型
	{
		type: 'childFemale',
		label: '儿童女',
		name: 'childFemale01',
		path: 'childFemale01.glb',
		texture: [
			{
				name: 'childFemale01',
				path: 'childFemale01.png',
			},
		],
	},
	{
		type: 'childFemale',
		label: '儿童女',
		name: 'childFemale02',
		path: 'childFemale02.glb',
		texture: [
			{
				name: 'childFemale02',
				path: 'childFemale02.png',
			},
		],
	},
	{
		type: 'childFemale',
		label: '儿童女',
		name: 'childFemale03',
		path: 'childFemale03.glb',
		texture: [
			{
				name: 'childFemale03',
				path: 'childFemale03.png',
			},
		],
	},
];

// 工具函数：根据类型随机获取一个模型
export const getRandomModelByType = (type) => {
	const filteredModels = modelList.filter((model) => model.type === type);
	if (!filteredModels.length) return null;

	const randomIndex = Math.floor(Math.random() * filteredModels.length);
	return {
		model: filteredModels[randomIndex].name,
		texture: filteredModels[randomIndex].texture,
	};
};
export const modelTypes = [
	'adultMale',
	'adultFemale',
	'childMale',
	'childFemale',
	'elderMale',
	'elderFemale',
];

// 工具函数：根据类型随机获取一个贴图
export const getRandomTextureByType = (type) => {
	const filteredModels = modelList.filter((model) => model.type === type);
	if (!filteredModels.length) return null;

	const randomIndex = Math.floor(Math.random() * filteredModels.length);
	return {
		name: filteredModels[randomIndex].name,
		path: filteredModels[randomIndex].texturePath,
	};
};

export default modelList;
